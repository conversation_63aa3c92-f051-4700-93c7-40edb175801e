<div>
    <form wire:submit.prevent="store">

        <div class="card-body">
            <div class="row">
                <div class="form-group col-12">
                    <label class="font-size-lg font-weight-bold">Destination Fax Numbers</label>
                    <small class="form-text text-muted mb-3">Add multiple fax numbers to send Scripts to multiple
                        destinations at once.</small>

                    <!-- Existing Fax Numbers -->
                    <div class="mb-5">
                        @if ($faxNumbers && count($faxNumbers) > 0)
                            @foreach ($faxNumbers as $index => $faxNumber)
                                <div class="d-flex mb-3 p-3 border rounded">
                                    <div class="mr-3 d-flex flex-row w-100">
                                        <div class="mr-3 flex-fill">
                                            <input type="text" wire:model="faxNumbers.{{ $index }}.label"
                                                class="form-control @error('faxNumbers.' . $index . '.label') is-invalid @enderror"
                                                placeholder="Enter Label">
                                            @error('faxNumbers.' . $index . '.label')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mr-3 flex-fill">
                                            <input type="text" wire:model="faxNumbers.{{ $index }}.numbers"
                                                class="form-control @error('faxNumbers.' . $index . '.numbers') is-invalid @enderror"
                                                placeholder="Enter fax number (e.g., 1234567890)" maxlength="20">
                                            @error('faxNumbers.' . $index . '.numbers')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                    </div>
                                    <button type="button" class="mh-50 btn btn-sm btn-light-danger" style="max-height: fit-content;"
                                        onclick="deleteFaxNumber({{ $index }})"
                                        title="Delete this fax number"
                                        @if (empty($faxNumber['numbers'])) disabled @endif>
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            @endforeach
                        @else
                            <div class="alert"
                                style="background-color: #dff9ff; border-color: #b8e6f2; color: #0c5460;">
                                <i class="fas fa-info-circle mr-2" style="color: black;"></i>
                                No fax numbers configured. Add your first destination fax number below.
                            </div>
                        @endif
                    </div>

                    <!-- New Fax Number Input Fields -->
                    @if ($newFaxInputs && count($newFaxInputs) > 0)
                        <div class="border-top pt-4 mb-4">
                            <h6 class="font-weight-bold mb-3">New Fax Numbers</h6>
                            @foreach ($newFaxInputs as $index => $newFaxInput)
                                <div class="d-flex align-items-center mb-3 p-3 border rounded bg-light">
                                    <div class="d-flex flex-row mr-3 w-100">
                                        <div class="mr-3 flex-fill">
                                            <input type="text" wire:model="newFaxInputs.{{ $index }}.label"
                                                class="form-control @error('newFaxInputs.' . $index . '.label') is-invalid @enderror"
                                                placeholder="Enter Label">
                                            @error('newFaxInputs.' . $index . '.label')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <div class="mr-3 flex-fill">
                                            <input type="text" wire:model="newFaxInputs.{{ $index }}.number"
                                                class="form-control @error('newFaxInputs.' . $index . '.number') is-invalid @enderror"
                                                placeholder="Enter fax number (e.g., 1234567890)" maxlength="12"
                                                inputmode="numeric">
                                            @error('newFaxInputs.' . $index . '.number')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>

                                        <button type="button" class="btn btn-sm btn-light-danger" style="max-height: fit-content;"
                                            onclick="removeNewFaxInput({{ $index }})"
                                            title="Remove this fax number field">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            @endforeach
                            <small class="form-text text-muted mb-3">
                                <i class="fas fa-info-circle mr-1"></i>
                                These fax numbers will be saved when you click "Save Fax Numbers" button.
                            </small>
                        </div>
                    @endif

                    <!-- Add New Fax Number Section -->
                    <div class="border-top pt-4">
                        <h6 class="font-weight-bold mb-3">Add New Fax Number</h6>
                        <div class="d-flex align-items-center">
                            <button type="button" class="btn btn-primary" wire:click="addNewFaxNumber">
                                <i class="fas fa-plus mr-1"></i> Add Number Field
                            </button>
                        </div>
                        <small class="form-text text-muted">
                            Click to add a new fax number input field, then fill it in and save.
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-footer d-flex justify-content-between align-items-center">
            <x-btn-with-loading wire:click="store" target="store" text="Save Fax Numbers" type="submit" />
        </div>

    </form>


    <script>
        function deleteFaxNumber(index) {
            // Check if the fax number field is empty
            const faxNumberInput = document.querySelector(`input[wire\\:model="faxNumbers.${index}.numbers"]`);
            if (faxNumberInput && !faxNumberInput.value.trim()) {
                Swal.fire({
                    title: 'Cannot Delete',
                    text: "Cannot delete empty fax number. Please enter a valid number first.",
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return;
            }

            Swal.fire({
                title: 'Are you sure?',
                text: "You want to delete this fax number?",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, Delete it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    @this.call('deleteFaxNumber', index);
                }
            });
        }

        function removeNewFaxInput(index) {
            Swal.fire({
                title: 'Remove Field?',
                text: "Are you sure you want to remove this fax number field?",
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, Remove it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    @this.call('removeNewFaxInput', index);
                }
            });
        }
    </script>
