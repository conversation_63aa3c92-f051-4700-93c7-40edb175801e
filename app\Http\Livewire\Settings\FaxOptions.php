<?php

namespace App\Http\Livewire\Settings;

use App\Models\FaxNumbers;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Livewire\Component;

class FaxOptions extends Component
{
    public $faxNumbers = [];
    public $newFaxNumber = '';
    public $newFaxInputs = []; // Array to store temporary new fax inputs

    protected $rules = [
        'faxNumbers.*.numbers' => 'required|string|min:11|max:12|regex:/^\d{10,12}$/',
        'faxNumbers.*.label' => 'required|string|max:225',
        'newFaxInputs' => 'required',
        'newFaxInputs.*.number' => 'required|string|min:11|max:12|regex:/^\d{10,12}$/',
        'newFaxInputs.*.label' => 'required|string|max:225',
    ];

    protected $messages = [
        'faxNumbers.*.numbers.required' => 'Fax number cannot be empty.',
        'faxNumbers.*.numbers.max' => 'Fax number cannot exceed 12 characters.',
        'faxNumbers.*.numbers.min' => 'Fax number must be at least 11 characters.',
        'faxNumbers.*.numbers.regex' => 'Fax number must be a valid number (11-12 digits only).',
        'faxNumbers.*.label.required' => 'Label is required.',
        'faxNumbers.*.label.max' => 'Label cannot exceed 225 characters.',

        'newFaxInputs.*.label' => 'Label is required.',
        'newFaxInputs.*.label.max' => 'Label cannot exceed 225 characters.',
        'newFaxInputs.*.number' => 'Fax number is required.',
        'newFaxInputs.*.number.max' => 'Fax number cannot exceed 12 characters.',
        'newFaxInputs.*.number.min' => 'Fax number must be at least 11 characters.',
    ];

    public function mount()
    {
        $this->loadFaxNumbers();
    }

    public function loadFaxNumbers()
    {
        $this->faxNumbers = FaxNumbers::where('is_active', 1)
            ->get()
            ->map(function ($fax) {
                return [
                    'id' => $fax->id,
                    'numbers' => $fax->numbers,
                    'label' => $fax->label,
                ];
            })
            ->toArray();
    }

    public function addNewFaxNumber()
    {
        // Just add a new empty input field to the temporary inputs array
        $this->newFaxInputs[] = [
            'number' => '',
            'label' => ''
        ];

        $this->emit('alert', ['type' => 'success', 'message' => 'New fax number field added. Fill it in and click Save to store.']);
    }

    public function removeNewFaxInput($index)
    {
        if (isset($this->newFaxInputs[$index])) {
            unset($this->newFaxInputs[$index]);
            // Re-index the array to maintain proper indexing
            $this->newFaxInputs = array_values($this->newFaxInputs);

            // Clear related validation errors
            $this->resetErrorBag("newFaxInputs.$index.label");
            $this->resetErrorBag("newFaxInputs.$index.number");

            $this->emit('alert', ['type' => 'success', 'message' => 'Fax number field removed.']);
        }
    }

    public function deleteFaxNumber($index)
    {
        if (isset($this->faxNumbers[$index])) {
            // Check if the fax number is empty
            if (empty($this->faxNumbers[$index]['numbers'])) {
                $this->emit('alert', ['type' => 'error', 'message' => 'Cannot delete empty fax number. Please enter a valid number or remove the field.']);
                return;
            }

            // If it's an existing record, delete from database
            if (isset($this->faxNumbers[$index]['id']) && $this->faxNumbers[$index]['id']) {
                $faxNumber = FaxNumbers::find($this->faxNumbers[$index]['id']);
                if ($faxNumber) {
                    $faxNumber->delete();
                }
            }

            // Remove from array
            unset($this->faxNumbers[$index]);
            $this->faxNumbers = array_values($this->faxNumbers); // reindex the array

            $this->emit('alert', ['type' => 'success', 'message' => 'Fax number deleted successfully.']);
        }
    }

    public function store()
    {
        $this->validate();

        try {
            $allNumbers = [];
            foreach ($this->faxNumbers as $item) {
                if (!empty($item['numbers'])) {
                    $allNumbers[] = $item['numbers'];
                }
            }

            foreach ($this->newFaxInputs as $item) {
                if (!empty($item['number'])) {
                    $allNumbers[] = $item['number'];
                }
            }

            if (count($allNumbers) !== count(array_unique($allNumbers))) {
                $this->addError('duplicate_numbers', 'Duplicate fax numbers are not allowed.');
                return;
            }

            // Step 3: Check against database
            foreach ($this->newFaxInputs as $index => $input) {
                if (!empty($input['number'])) {
                    $exists = \App\Models\FaxNumbers::where('number', $input['number'])->exists();
                    if ($exists) {
                        $this->addError("newFaxInputs.$index.number", 'This fax number already exists in the database.');
                        return;
                    }
                }
            }
            // $this->validate();
            // Debug logging
            Log::info('FaxOptions store method called', [
                'faxNumbers' => $this->faxNumbers,
                'newFaxInputs' => $this->newFaxInputs
            ]);

            $savedCount = 0;

            // Process existing fax numbers
            foreach ($this->faxNumbers as $faxData) {
                if (!empty(trim($faxData['numbers'])) && !empty(trim($faxData['label']))) {
                    if (isset($faxData['id']) && $faxData['id']) {
                        // Update existing record
                        FaxNumbers::where('id', $faxData['id'])
                            ->update([
                                'numbers' => trim($faxData['numbers']),
                                'label' => trim($faxData['label']),
                                'is_active' => 1
                            ]);
                        $savedCount++;
                    } else {
                        // Create new record
                        FaxNumbers::create([
                            'numbers' => trim($faxData['numbers']),
                            'label' => trim($faxData['label']),
                            'is_active' => 1
                        ]);
                        $savedCount++;
                    }
                }
            }

            // Process new fax inputs
            foreach ($this->newFaxInputs as $newFaxData) {
                if (!empty(trim($newFaxData['number'])) && !empty(trim($newFaxData['label']))) {
                    FaxNumbers::create([
                        'numbers' => trim($newFaxData['number']),
                        'label' => trim($newFaxData['label']),
                        'is_active' => 1
                    ]);
                    $savedCount++;
                }
            }

            // Clear the temporary inputs after saving
            $this->newFaxInputs = [];

            $this->emit('alert', ['type' => 'success', 'message' => "Fax numbers saved successfully. {$savedCount} fax number(s) processed."]);
            $this->loadFaxNumbers(); // Reload to get fresh data with IDs

        } catch (ValidationException $e) {
            // Validation errors are already handled by Livewire
            $this->emit('alert', ['type' => 'error', 'message' => 'Please fix the validation errors and try again.']);
        } catch (\Exception $e) {
            Log::error('Error saving fax numbers: ' . $e->getMessage(), [
                'faxNumbers' => $this->faxNumbers,
                'newFaxInputs' => $this->newFaxInputs,
                'trace' => $e->getTraceAsString()
            ]);
            $this->emit('alert', ['type' => 'error', 'message' => 'Error saving fax numbers: ' . $e->getMessage()]);
        }
    }

    public function updated($propertyName)
    {

        // Only validate the field if it has content
        $this->validateOnly($propertyName);

        // Emit contentChanged event to re-initialize Select2 after property updates
        $this->emit('contentChanged');
    }

    public function render()
    {
        return view('livewire.settings.fax-options');
    }
}
